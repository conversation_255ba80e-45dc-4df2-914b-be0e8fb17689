"use client";

import * as React from "react";
import { Search as SearchIcon } from "lucide-react";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@workspace/ui/components/popover";
import { cn } from "@workspace/ui/lib/utils";

export interface SearchProps {
  /**
   * Placeholder text for the search input
   */
  placeholder?: string;
  /**
   * Value of the search input
   */
  value?: string;
  /**
   * <PERSON><PERSON> fired when the search value changes
   */
  onValueChange?: (value: string) => void;
  /**
   * <PERSON><PERSON> fired when the search is submitted (Enter key or search button)
   */
  onSearch?: (value: string) => void;
  /**
   * Whether the search popover is open
   */
  open?: boolean;
  /**
   * <PERSON><PERSON> fired when the open state changes
   */
  onOpenChange?: (open: boolean) => void;
  /**
   * Size of the search button
   */
  size?: "default" | "small";
  /**
   * Alignment of the popover content
   */
  align?: "start" | "center" | "end";
  side?: "top" | "right" | "bottom" | "left";
  /**
   * Additional className for the popover content
   */
  className?: string;
  /**
   * Additional className for the search input
   */
  inputClassName?: string;
  /**
   * Additional className for the search button
   */
  buttonClassName?: string;
  /**
   * Whether the search input should auto-focus when opened
   */
  autoFocus?: boolean;
  /**
   * Whether to show the search button
   */
  showButton?: boolean;
  /**
   * Container element for the popover portal
   */
  container?: HTMLElement | null;
}

export const Search = React.forwardRef<HTMLInputElement, SearchProps>(
  (
    {
      placeholder = "Ara",
      value,
      onValueChange,
      onSearch,
      open,
      onOpenChange,
      size = "small",
      side = "bottom",
      align = "end",
      className = "w-44",
      inputClassName,
      buttonClassName,
      autoFocus = true,
      showButton = true,
      container,
      ...props
    },
    ref,
  ) => {
    const [internalValue, setInternalValue] = React.useState(value || "");
    const [internalOpen, setInternalOpen] = React.useState(false);

    const isControlled = value !== undefined;
    const currentValue = isControlled ? value : internalValue;
    const currentOpen = open !== undefined ? open : internalOpen;

    const handleValueChange = (newValue: string) => {
      if (!isControlled) {
        setInternalValue(newValue);
      }
      onValueChange?.(newValue);
    };

    const handleOpenChange = (newOpen: boolean) => {
      if (open === undefined) {
        setInternalOpen(newOpen);
      }
      onOpenChange?.(newOpen);

      // Focus the input when opening
      if (newOpen && ref && "current" in ref && ref.current) {
        setTimeout(() => {
          ref.current?.focus();
        }, 0);
      }
    };

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === "Enter") {
        e.preventDefault();
        onSearch?.(currentValue);
        handleOpenChange(false);
      }
    };

    if (!showButton) {
      return (
        <Input
          ref={ref}
          placeholder={placeholder}
          value={currentValue}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            handleValueChange(e.target.value)
          }
          onKeyDown={handleKeyDown}
          className={cn(
            "outline-none border-2 border-border rounded-lg",
            inputClassName,
          )}
          {...props}
        />
      );
    }

    return (
      <Popover open={currentOpen} onOpenChange={handleOpenChange}>
        <PopoverTrigger asChild>
          <Button size={size} className={buttonClassName}>
            <SearchIcon className="size-6 stroke-2" />
          </Button>
        </PopoverTrigger>
        <PopoverContent
          align={align}
          side={side}
          container={container}
          className={cn(
            className,
            "p-1 border-2 border-red-500 pointer-events-auto bg-white",
          )}
          onInteractOutside={(e) => {
            // Prevent closing when clicking on the trigger button
            const target = e.target as Element;
            if (target.closest('[data-slot="popover-trigger"]')) {
              e.preventDefault();
            }
          }}
        >
          <Input
            ref={ref}
            placeholder={placeholder}
            value={currentValue}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              handleValueChange(e.target.value)
            }
            onKeyDown={handleKeyDown}
            className={cn(
              "outline-none border-2 border-blue-500 rounded-lg cursor-text bg-yellow-100 pointer-events-auto",
              inputClassName,
            )}
            autoFocus={autoFocus}
            {...props}
          />
        </PopoverContent>
      </Popover>
    );
  },
);

Search.displayName = "Search";
